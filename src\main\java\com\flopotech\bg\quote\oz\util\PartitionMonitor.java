package com.flopotech.bg.quote.oz.util;

import org.apache.kafka.clients.producer.Callback;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 分区监控工具
 * 用于监控和统计消息分区分布情况
 */
@Component
public class PartitionMonitor {

    private static final Logger logger = LoggerFactory.getLogger(PartitionMonitor.class);

    // 统计每个symbol发送到的分区
    private final ConcurrentHashMap<String, Integer> symbolPartitionMap = new ConcurrentHashMap<>();
    
    // 统计每个分区的消息数量
    private final ConcurrentHashMap<Integer, AtomicLong> partitionMessageCount = new ConcurrentHashMap<>();
    
    // 统计每个symbol的消息数量
    private final ConcurrentHashMap<String, AtomicLong> symbolMessageCount = new ConcurrentHashMap<>();

    /**
     * 创建一个回调函数来监控消息发送结果
     *
     * @param symbol 合约符号
     * @return Kafka发送回调
     */
    public Callback createMonitorCallback(String symbol) {
        return new Callback() {
            @Override
            public void onCompletion(RecordMetadata metadata, Exception exception) {
                if (exception == null) {
                    // 记录成功发送的消息
                    recordMessage(symbol, metadata.partition());
                    
                    logger.debug("消息发送成功 - Symbol: {}, Partition: {}, Offset: {}, Topic: {}",
                            symbol, metadata.partition(), metadata.offset(), metadata.topic());
                } else {
                    logger.error("消息发送失败 - Symbol: {}, Error: {}", symbol, exception.getMessage(), exception);
                }
            }
        };
    }

    /**
     * 记录消息分区信息
     *
     * @param symbol    合约符号
     * @param partition 分区号
     */
    private void recordMessage(String symbol, int partition) {
        // 记录symbol对应的分区
        symbolPartitionMap.put(symbol, partition);
        
        // 统计分区消息数量
        partitionMessageCount.computeIfAbsent(partition, k -> new AtomicLong(0)).incrementAndGet();
        
        // 统计symbol消息数量
        symbolMessageCount.computeIfAbsent(symbol, k -> new AtomicLong(0)).incrementAndGet();
    }

    /**
     * 获取symbol对应的分区
     *
     * @param symbol 合约符号
     * @return 分区号，如果没有记录则返回-1
     */
    public int getPartitionForSymbol(String symbol) {
        return symbolPartitionMap.getOrDefault(symbol, -1);
    }

    /**
     * 获取分区的消息数量
     *
     * @param partition 分区号
     * @return 消息数量
     */
    public long getMessageCountForPartition(int partition) {
        AtomicLong count = partitionMessageCount.get(partition);
        return count != null ? count.get() : 0;
    }

    /**
     * 获取symbol的消息数量
     *
     * @param symbol 合约符号
     * @return 消息数量
     */
    public long getMessageCountForSymbol(String symbol) {
        AtomicLong count = symbolMessageCount.get(symbol);
        return count != null ? count.get() : 0;
    }

    /**
     * 打印分区统计信息
     */
    public void printPartitionStatistics() {
        logger.info("=== 分区统计信息 ===");
        
        logger.info("Symbol -> Partition 映射:");
        symbolPartitionMap.forEach((symbol, partition) -> {
            long messageCount = getMessageCountForSymbol(symbol);
            logger.info("  {} -> Partition {} (消息数: {})", symbol, partition, messageCount);
        });
        
        logger.info("分区消息数量统计:");
        partitionMessageCount.forEach((partition, count) -> {
            logger.info("  Partition {} : {} 条消息", partition, count.get());
        });
        
        logger.info("总计: {} 个Symbol, {} 个分区", 
                symbolPartitionMap.size(), partitionMessageCount.size());
    }

    /**
     * 验证分区一致性
     * 检查相同symbol的消息是否都发送到了同一个分区
     *
     * @return 是否一致
     */
    public boolean validatePartitionConsistency() {
        // 在实际应用中，这里可以添加更复杂的验证逻辑
        // 比如检查历史记录，确保同一symbol始终发送到同一分区
        
        boolean isConsistent = true;
        for (String symbol : symbolPartitionMap.keySet()) {
            int partition = symbolPartitionMap.get(symbol);
            logger.debug("Symbol {} 一致性检查: 分区 {}", symbol, partition);
        }
        
        return isConsistent;
    }

    /**
     * 清除统计信息
     */
    public void clearStatistics() {
        symbolPartitionMap.clear();
        partitionMessageCount.clear();
        symbolMessageCount.clear();
        logger.info("分区统计信息已清除");
    }

    /**
     * 获取所有已知的symbol
     *
     * @return symbol集合
     */
    public java.util.Set<String> getAllSymbols() {
        return symbolPartitionMap.keySet();
    }

    /**
     * 获取所有使用的分区
     *
     * @return 分区集合
     */
    public java.util.Set<Integer> getAllPartitions() {
        return partitionMessageCount.keySet();
    }
}
