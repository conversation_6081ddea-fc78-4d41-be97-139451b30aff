[DEFAULT]
ConnectionType=initiator
ReconnectInterval=30
FileStorePath=quickfix/onezero/store
FileLogPath=quickfix/onezero/log
ScreenLogShowIncoming=Y
ScreenLogShowOutgoing=Y
ScreenLogShowEvents=Y
StartTime=00:00:00
EndTime=00:00:00
UseDataDictionary=Y
DataDictionary=FIX44.xml
HeartBtInt=15
# 重置序号配置（解决序号不匹配问题）
ResetOnLogon=Y
ResetOnLogout=Y
ResetOnDisconnect=Y

[SESSION]
BeginString=FIX.4.4
# 使用与成功示例类似的CompID格式
SenderCompID=EBC_Technology_Q
TargetCompID=oneZero_Q
# 启用SSL/TLS连接
# 启用SSL/TLS连接（Trade环境）
SocketUseSSL=Y
# 指定加密套件和协议版本
CipherSuites=TLS_RSA_WITH_AES_128_CBC_SHA
EnabledProtocols=TLSv1.2
# 禁用证书验证（处理过期证书）
SocketCheckCertificate=N
SocketVerifyHostname=N
SocketTrustAllCertificates=Y
# SSL线程安全配置
SocketReuseAddress=Y
SocketTcpNoDelay=Y
SocketKeepAlive=Y
# 连接配置
SocketConnectHost=taker5-conformance-env.onezero.com
SocketConnectPort=10183
Password=NwzhXY9vkn
SocketConnectTimeout=30000
# 启用证书验证
# SSL证书配置 - 跳过证书验证（因为OneZero证书已过期）
SocketCheckCertificate=N
SocketVerifyHostname=N
# 信任所有证书（用于处理过期证书）
SocketTrustAllCertificates=Y
