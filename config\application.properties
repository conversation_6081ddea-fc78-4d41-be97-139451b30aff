spring.application.name=quote-oz-fix

server.port=8090
server.servlet.context-path=/fix

logging.config=classpath:logback-spring.xml
logging.level.com.fudianyun=DEBUG
logging.level.org.springframework=INFO
logging.level.org.apache=WARN
logging.file.path=logs
logging.charset.console=UTF-8
logging.charset.file=UTF-8

quickfix.client.config=classpath:quickfix-client.cfg
quickfix.log.enabled=true
quickfix.log.path=logs/quickfix

quickfix.onezero.auto-start=true
quickfix.onezero.password=NwzhXY9vkn
quickfix.onezero.subscribe.symbol=EURUSD,GBPUSD

# Kafka配置
spring.kafka.bootstrap-servers=localhost:9092
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.ByteArraySerializer
spring.kafka.producer.acks=all
spring.kafka.producer.retries=3
spring.kafka.producer.batch-size=16384
spring.kafka.producer.linger-ms=1
spring.kafka.producer.buffer-memory=33554432

spring.kafka.consumer.group-id=bg-oz-quote-group
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.ByteArrayDeserializer
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.enable-auto-commit=true
spring.kafka.consumer.auto-commit-interval=1000

# Kafka Topic配置
kafka.topic.market-data=market-data-topic
kafka.topic.partitions=3
kafka.topic.replication-factor=1

# 分区策略说明:
# 使用自定义SymbolPartitioner，按symbol进行分区
# 相同symbol的消息会发送到同一个分区，保证消息顺序
# 建议分区数设置为预期symbol数量的合理倍数
