package com.flopotech.bg.quote.oz.service;

import com.flopotech.bg.quote.oz.msg.MarketDataMsg;
import com.flopotech.bg.quote.oz.util.ProtobufSerializer;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;

import org.springframework.stereotype.Service;

/**
 * 市场数据Kafka消费者服务
 * 负责从Kafka Topic消费市场数据
 */
@Service
public class MarketDataConsumer {

    private static final Logger logger = LoggerFactory.getLogger(MarketDataConsumer.class);

    @Autowired
    private ProtobufSerializer protobufSerializer;

    /**
     * 消费市场数据消息
     *
     * @param record         消费记录
     * @param data           消息数据
     * @param key            消息key
     * @param partition      分区
     * @param offset         偏移量
     * @param acknowledgment 确认对象
     */
    @KafkaListener(topics = "${kafka.topic.market-data}", groupId = "${spring.kafka.consumer.group-id}")
    public void consumeMarketData(ConsumerRecord<String, byte[]> record, Acknowledgment acknowledgment) {

        try {
            String key = record.key();
            int partition = record.partition();
            long offset = record.offset();
            byte[] data = record.value();

            logger.debug("接收到市场数据消息 - Key: {}, Partition: {}, Offset: {}, Size: {} bytes",
                    key, partition, offset, data.length);

            // 反序列化protobuf消息
            MarketDataMsg.MarketData marketData = protobufSerializer.deserialize(
                    data, MarketDataMsg.MarketData.parser());

            if (marketData != null) {
                // 处理市场数据
                processMarketData(marketData, key, partition, offset);

                // 手动确认消息
                if (acknowledgment != null) {
                    acknowledgment.acknowledge();
                }

                logger.debug("成功处理市场数据 - Symbol: {}, Timestamp: {}, Bid: {}, Offer: {}",
                        marketData.getSymbol(), marketData.getTimestamp(),
                        marketData.getBid(), marketData.getOffer());
            } else {
                logger.warn("反序列化市场数据失败 - Key: {}, Partition: {}, Offset: {}",
                        key, partition, offset);
            }

        } catch (Exception e) {
            logger.error("处理市场数据消息异常 - Key: {}, Partition: {}, Offset: {}, Error: {}",
                    record.key(), record.partition(), record.offset(), e.getMessage(), e);

            // 根据业务需求决定是否确认消息
            // 这里选择确认，避免重复处理相同的错误消息
            if (acknowledgment != null) {
                acknowledgment.acknowledge();
            }
        }
    }

    /**
     * 处理市场数据业务逻辑
     *
     * @param marketData 市场数据
     * @param key        消息key
     * @param partition  分区
     * @param offset     偏移量
     */
    private void processMarketData(MarketDataMsg.MarketData marketData,
            String key, int partition, long offset) {

        // 这里可以添加具体的业务处理逻辑
        // 例如：
        // 1. 数据验证
        // 2. 存储到数据库
        // 3. 发送到其他系统
        // 4. 实时计算
        // 5. 风险控制等

        logger.info("处理市场数据 - Symbol: {}, Bid: {}, Offer: {}, Mid: {}, DataCount: {}",
                marketData.getSymbol(),
                marketData.getBid(),
                marketData.getOffer(),
                marketData.getMid(),
                marketData.getDataCount());

        // 处理深度数据
        if (marketData.getDataCount() > 0) {
            marketData.getDataList().forEach(depthData -> {
                logger.debug("深度数据 - Type: {}, Price: {}, Size: {}, Condition: {}, Origin: {}",
                        depthData.getTp(),
                        depthData.getPx(),
                        depthData.getSz(),
                        depthData.getCondition(),
                        depthData.getOri());
            });
        }
    }

    /**
     * 处理消费异常
     */
    @KafkaListener(topics = "${kafka.topic.market-data}.DLT", groupId = "${spring.kafka.consumer.group-id}-dlt")
    public void handleDltMarketData(ConsumerRecord<String, byte[]> record) {

        logger.error("处理死信队列消息 - Key: {}, Partition: {}, Offset: {}, Size: {} bytes",
                record.key(), record.partition(), record.offset(), record.value().length);

        // 这里可以添加死信队列的处理逻辑
        // 例如：记录到数据库、发送告警等
    }
}
