package com.flopotech.bg.quote.oz.kafka;

import org.apache.kafka.clients.producer.Partitioner;
import org.apache.kafka.common.Cluster;
import org.apache.kafka.common.PartitionInfo;
import org.apache.kafka.common.utils.Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * 基于Symbol的自定义分区器
 * 确保相同symbol的消息发送到同一个分区
 */
public class SymbolPartitioner implements Partitioner {

    private static final Logger logger = LoggerFactory.getLogger(SymbolPartitioner.class);

    @Override
    public int partition(String topic, Object key, byte[] keyBytes, Object value, byte[] valueBytes, Cluster cluster) {
        List<PartitionInfo> partitions = cluster.partitionsForTopic(topic);
        int numPartitions = partitions.size();

        if (key == null) {
            // 如果key为null，使用轮询方式
            logger.warn("消息key为null，使用轮询分区策略");
            return (int) (Math.random() * numPartitions);
        }

        String symbol = key.toString();
        
        // 使用symbol的hash值来确定分区
        // 这样可以确保相同symbol的消息总是发送到同一个分区
        int partition = Math.abs(Utils.murmur2(symbol.getBytes())) % numPartitions;
        
        logger.debug("Symbol: {} 分配到分区: {} (总分区数: {})", symbol, partition, numPartitions);
        
        return partition;
    }

    @Override
    public void close() {
        // 清理资源，如果需要的话
        logger.info("SymbolPartitioner 关闭");
    }

    @Override
    public void configure(Map<String, ?> configs) {
        // 配置分区器，如果需要的话
        logger.info("SymbolPartitioner 配置完成");
    }
}
