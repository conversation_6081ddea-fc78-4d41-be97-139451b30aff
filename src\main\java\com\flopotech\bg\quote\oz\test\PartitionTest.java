package com.flopotech.bg.quote.oz.test;

import com.flopotech.bg.quote.oz.service.MarketDataService;
import com.flopotech.bg.quote.oz.util.PartitionMonitor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * 分区测试工具
 * 测试symbol分区策略是否正确工作
 */
@Component
@Order(2) // 在KafkaProtobufExample之后运行
public class PartitionTest implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(PartitionTest.class);

    @Autowired
    private MarketDataService marketDataService;

    @Autowired
    private PartitionMonitor partitionMonitor;

    // 测试用的symbol列表
    private static final List<String> TEST_SYMBOLS = Arrays.asList(
            "EURUSD", "GBPUSD", "USDJPY", "USDCHF", "AUDUSD", 
            "USDCAD", "NZDUSD", "EURGBP", "EURJPY", "GBPJPY"
    );

    @Override
    public void run(String... args) throws Exception {
        // 等待一段时间，确保之前的示例消息已经处理完成
        Thread.sleep(2000);
        
        logger.info("=== 开始分区测试 ===");

        // 清除之前的统计信息
        partitionMonitor.clearStatistics();

        // 测试1：发送多个symbol的消息，验证分区分配
        testSymbolPartitioning();

        // 等待消息发送完成
        Thread.sleep(3000);

        // 测试2：验证相同symbol的消息是否发送到同一分区
        testPartitionConsistency();

        // 等待消息发送完成
        Thread.sleep(3000);

        // 打印分区统计信息
        partitionMonitor.printPartitionStatistics();

        // 验证分区一致性
        boolean isConsistent = partitionMonitor.validatePartitionConsistency();
        logger.info("分区一致性验证结果: {}", isConsistent ? "通过" : "失败");

        logger.info("=== 分区测试完成 ===");
    }

    /**
     * 测试symbol分区分配
     */
    private void testSymbolPartitioning() {
        logger.info("--- 测试Symbol分区分配 ---");

        Random random = new Random();
        
        // 为每个symbol发送一条消息
        for (String symbol : TEST_SYMBOLS) {
            double bid = 1.0000 + random.nextDouble() * 0.5;
            double offer = bid + 0.0002 + random.nextDouble() * 0.0008;
            
            marketDataService.processAndSendMarketData(
                    symbol, bid, offer, System.currentTimeMillis());
            
            logger.debug("发送测试消息: Symbol={}, Bid={}, Offer={}", symbol, bid, offer);
        }
        
        logger.info("已发送 {} 个不同symbol的测试消息", TEST_SYMBOLS.size());
    }

    /**
     * 测试分区一致性
     * 为相同symbol发送多条消息，验证是否都发送到同一分区
     */
    private void testPartitionConsistency() {
        logger.info("--- 测试分区一致性 ---");

        Random random = new Random();
        
        // 选择几个symbol，每个发送多条消息
        List<String> testSymbols = Arrays.asList("EURUSD", "GBPUSD", "USDJPY");
        
        for (String symbol : testSymbols) {
            logger.debug("为Symbol {} 发送多条消息测试一致性", symbol);
            
            // 为每个symbol发送5条消息
            for (int i = 0; i < 5; i++) {
                double bid = 1.0000 + random.nextDouble() * 0.5;
                double offer = bid + 0.0002 + random.nextDouble() * 0.0008;
                
                marketDataService.processAndSendMarketData(
                        symbol, bid, offer, System.currentTimeMillis());
                
                // 短暂延迟，避免消息发送过快
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
        
        logger.info("一致性测试完成，每个测试symbol发送了5条消息");
    }

    /**
     * 分析分区分布情况
     */
    public void analyzePartitionDistribution() {
        logger.info("=== 分区分布分析 ===");
        
        // 获取所有symbol和分区信息
        var allSymbols = partitionMonitor.getAllSymbols();
        var allPartitions = partitionMonitor.getAllPartitions();
        
        logger.info("总计Symbol数量: {}", allSymbols.size());
        logger.info("使用的分区数量: {}", allPartitions.size());
        
        // 分析每个分区的负载
        for (Integer partition : allPartitions) {
            long messageCount = partitionMonitor.getMessageCountForPartition(partition);
            logger.info("分区 {} 负载: {} 条消息", partition, messageCount);
        }
        
        // 检查是否有分区负载不均衡
        if (allPartitions.size() > 1) {
            long maxLoad = allPartitions.stream()
                    .mapToLong(partitionMonitor::getMessageCountForPartition)
                    .max().orElse(0);
            long minLoad = allPartitions.stream()
                    .mapToLong(partitionMonitor::getMessageCountForPartition)
                    .min().orElse(0);
            
            double loadRatio = maxLoad > 0 ? (double) minLoad / maxLoad : 1.0;
            logger.info("分区负载均衡度: {:.2f} (1.0为完全均衡)", loadRatio);
            
            if (loadRatio < 0.5) {
                logger.warn("检测到分区负载不均衡，建议检查分区策略");
            }
        }
    }
}
