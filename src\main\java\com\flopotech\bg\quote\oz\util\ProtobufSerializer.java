package com.flopotech.bg.quote.oz.util;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;
import com.google.protobuf.Parser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * Protobuf序列化工具类
 * 用于Kafka消息的序列化和反序列化
 */
@Component
public class ProtobufSerializer {

    private static final Logger logger = LoggerFactory.getLogger(ProtobufSerializer.class);

    /**
     * 序列化Protobuf消息为字节数组
     *
     * @param message Protobuf消息对象
     * @return 序列化后的字节数组
     */
    public byte[] serialize(Message message) {
        if (message == null) {
            logger.warn("尝试序列化null消息");
            return null;
        }

        try {
            byte[] data = message.toByteArray();
            logger.debug("成功序列化消息，类型: {}, 大小: {} bytes", 
                        message.getClass().getSimpleName(), data.length);
            return data;
        } catch (Exception e) {
            logger.error("序列化消息失败: {}", e.getMessage(), e);
            throw new RuntimeException("序列化Protobuf消息失败", e);
        }
    }

    /**
     * 反序列化字节数组为Protobuf消息
     *
     * @param data   字节数组
     * @param parser Protobuf消息解析器
     * @param <T>    消息类型
     * @return 反序列化后的消息对象
     */
    public <T extends Message> T deserialize(byte[] data, Parser<T> parser) {
        if (data == null || data.length == 0) {
            logger.warn("尝试反序列化空数据");
            return null;
        }

        try {
            T message = parser.parseFrom(data);
            logger.debug("成功反序列化消息，类型: {}, 大小: {} bytes", 
                        message.getClass().getSimpleName(), data.length);
            return message;
        } catch (InvalidProtocolBufferException e) {
            logger.error("反序列化消息失败: {}", e.getMessage(), e);
            throw new RuntimeException("反序列化Protobuf消息失败", e);
        }
    }

    /**
     * 检查数据是否为有效的Protobuf格式
     *
     * @param data   字节数组
     * @param parser Protobuf消息解析器
     * @return 是否有效
     */
    public boolean isValidProtobuf(byte[] data, Parser<? extends Message> parser) {
        if (data == null || data.length == 0) {
            return false;
        }

        try {
            parser.parseFrom(data);
            return true;
        } catch (InvalidProtocolBufferException e) {
            logger.debug("数据不是有效的Protobuf格式: {}", e.getMessage());
            return false;
        }
    }
}
