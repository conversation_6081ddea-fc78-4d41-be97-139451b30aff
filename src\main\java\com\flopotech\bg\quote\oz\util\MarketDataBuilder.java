package com.flopotech.bg.quote.oz.util;

import com.flopotech.bg.quote.oz.msg.MarketDataMsg;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 市场数据构建器工具类
 * 用于方便地创建MarketData protobuf消息
 */
@Component
public class MarketDataBuilder {

    /**
     * 创建市场数据消息
     *
     * @param timestamp 时间戳
     * @param symbol    合约
     * @param bid       最优bid价
     * @param offer     最优offer价
     * @return MarketData消息
     */
    public MarketDataMsg.MarketData createMarketData(
            long timestamp, String symbol, double bid, double offer) {

        double mid = (bid + offer) / 2.0;

        return MarketDataMsg.MarketData.newBuilder()
                .setTimestamp(timestamp)
                .setSymbol(symbol)
                .setBid(bid)
                .setOffer(offer)
                .setMid(mid)
                .build();
    }

    /**
     * 创建包含深度数据的市场数据消息
     *
     * @param timestamp     时间戳
     * @param symbol        合约
     * @param bid           最优bid价
     * @param offer         最优offer价
     * @param depthDataList 深度数据列表
     * @return MarketData消息
     */
    public MarketDataMsg.MarketData createMarketDataWithDepth(
            long timestamp, String symbol, double bid, double offer,
            List<MarketDataMsg.DepthData> depthDataList) {

        double mid = (bid + offer) / 2.0;

        MarketDataMsg.MarketData.Builder builder = MarketDataMsg.MarketData.newBuilder()
                .setTimestamp(timestamp)
                .setSymbol(symbol)
                .setBid(bid)
                .setOffer(offer)
                .setMid(mid);

        if (depthDataList != null && !depthDataList.isEmpty()) {
            builder.addAllData(depthDataList);
        }

        return builder.build();
    }

    /**
     * 创建深度数据
     *
     * @param tp        价格类型 (0-bid, 1-offer)
     * @param px        价格
     * @param sz        可交易量
     * @param condition 报价条件
     * @param ori       发起者
     * @param uqId      报价单唯一标识
     * @return DepthData消息
     */
    public MarketDataMsg.DepthData createDepthData(
            int tp, double px, double sz, String condition, String ori, String uqId) {

        return MarketDataMsg.DepthData.newBuilder()
                .setTp(tp)
                .setPx(px)
                .setSz(sz)
                .setCondition(condition != null ? condition : "")
                .setOri(ori != null ? ori : "")
                .setUqId(uqId != null ? uqId : "")
                .build();
    }

    /**
     * 创建可交易报价深度数据
     *
     * @param tp   价格类型 (0-bid, 1-offer)
     * @param px   价格
     * @param sz   可交易量
     * @param ori  发起者
     * @param uqId 报价单唯一标识
     * @return DepthData消息
     */
    public MarketDataMsg.DepthData createTradeableDepthData(
            int tp, double px, double sz, String ori, String uqId) {

        return createDepthData(tp, px, sz, "A", ori, uqId);
    }

    /**
     * 创建参考报价深度数据
     *
     * @param tp   价格类型 (0-bid, 1-offer)
     * @param px   价格
     * @param sz   可交易量
     * @param ori  发起者
     * @param uqId 报价单唯一标识
     * @return DepthData消息
     */
    public MarketDataMsg.DepthData createIndicativeDepthData(
            int tp, double px, double sz, String ori, String uqId) {

        return createDepthData(tp, px, sz, "I", ori, uqId);
    }

    /**
     * 从现有MarketData创建新的MarketData（用于更新）
     *
     * @param original 原始MarketData
     * @param newBid   新的bid价
     * @param newOffer 新的offer价
     * @return 更新后的MarketData消息
     */
    public MarketDataMsg.MarketData updateMarketData(
            MarketDataMsg.MarketData original, double newBid, double newOffer) {

        double newMid = (newBid + newOffer) / 2.0;

        return MarketDataMsg.MarketData.newBuilder(original)
                .setBid(newBid)
                .setOffer(newOffer)
                .setMid(newMid)
                .setTimestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 验证MarketData消息的有效性
     *
     * @param marketData MarketData消息
     * @return 是否有效
     */
    public boolean isValidMarketData(MarketDataMsg.MarketData marketData) {
        if (marketData == null) {
            return false;
        }

        // 检查必要字段
        if (marketData.getSymbol() == null || marketData.getSymbol().trim().isEmpty()) {
            return false;
        }

        if (marketData.getTimestamp() <= 0) {
            return false;
        }

        // 检查价格的合理性
        if (marketData.getBid() <= 0 || marketData.getOffer() <= 0) {
            return false;
        }

        if (marketData.getBid() >= marketData.getOffer()) {
            return false;
        }

        return true;
    }
}
