# Kafka Symbol分区策略说明

## 概述

本项目实现了基于Symbol（合约符号）的Kafka消息分区策略，确保相同symbol的市场数据消息始终发送到同一个分区，从而保证消息的顺序性。

## 核心组件

### 1. SymbolPartitioner (自定义分区器)
- **位置**: `com.flopotech.bg.quote.oz.kafka.SymbolPartitioner`
- **功能**: 根据消息的key（symbol）计算分区号
- **算法**: 使用MurmurHash2算法对symbol进行hash，然后取模得到分区号

```java
int partition = Math.abs(Utils.murmur2(symbol.getBytes())) % numPartitions;
```

### 2. KafkaConfig (Kafka配置)
- **位置**: `com.flopotech.bg.quote.oz.config.KafkaConfig`
- **功能**: 配置Kafka Producer使用自定义分区器
- **关键配置**:
  ```java
  configProps.put(ProducerConfig.PARTITIONER_CLASS_CONFIG, SymbolPartitioner.class.getName());
  ```

### 3. PartitionMonitor (分区监控)
- **位置**: `com.flopotech.bg.quote.oz.util.PartitionMonitor`
- **功能**: 监控和统计消息分区分布情况
- **特性**:
  - 记录每个symbol对应的分区
  - 统计每个分区的消息数量
  - 验证分区一致性

## 配置说明

### application.properties配置
```properties
# Kafka Topic配置
kafka.topic.market-data=market-data-topic
kafka.topic.partitions=3
kafka.topic.replication-factor=1

# 分区策略说明:
# 使用自定义SymbolPartitioner，按symbol进行分区
# 相同symbol的消息会发送到同一个分区，保证消息顺序
# 建议分区数设置为预期symbol数量的合理倍数
```

### 分区数量建议
- **小规模**: 3-6个分区（适用于10-20个symbol）
- **中等规模**: 6-12个分区（适用于50-100个symbol）
- **大规模**: 12-24个分区（适用于100+个symbol）

## 使用方式

### 1. 发送消息
```java
@Autowired
private MarketDataService marketDataService;

// 发送市场数据，symbol作为分区key
marketDataService.processAndSendMarketData("EURUSD", 1.0850, 1.0852, System.currentTimeMillis());
```

### 2. 监控分区分布
```java
@Autowired
private PartitionMonitor partitionMonitor;

// 打印分区统计信息
partitionMonitor.printPartitionStatistics();

// 获取特定symbol的分区
int partition = partitionMonitor.getPartitionForSymbol("EURUSD");

// 验证分区一致性
boolean isConsistent = partitionMonitor.validatePartitionConsistency();
```

## 分区策略优势

### 1. 消息顺序保证
- 相同symbol的消息发送到同一分区
- 在分区内保证消息的顺序性
- 避免乱序导致的数据不一致

### 2. 负载均衡
- 不同symbol分布到不同分区
- 提高并行处理能力
- 避免热点分区问题

### 3. 扩展性
- 支持动态增加分区数量
- 新symbol自动分配到合适分区
- 支持高并发场景

## 测试验证

### 运行分区测试
项目包含了完整的分区测试工具：

```java
// 自动运行的测试组件
@Component
@Order(2)
public class PartitionTest implements CommandLineRunner
```

### 测试内容
1. **Symbol分区分配测试**: 验证不同symbol是否正确分配到分区
2. **分区一致性测试**: 验证相同symbol的多条消息是否发送到同一分区
3. **负载均衡分析**: 分析各分区的消息分布情况

### 查看测试结果
启动应用后，查看日志输出：
```
=== 分区统计信息 ===
Symbol -> Partition 映射:
  EURUSD -> Partition 0 (消息数: 6)
  GBPUSD -> Partition 1 (消息数: 6)
  USDJPY -> Partition 2 (消息数: 6)
...
分区一致性验证结果: 通过
```

## 注意事项

### 1. 分区数量规划
- 分区数量一旦设定，不建议频繁修改
- 考虑未来symbol增长情况
- 平衡并行度和资源消耗

### 2. 消费者配置
- 消费者数量不应超过分区数量
- 建议消费者数量等于分区数量以获得最佳性能

### 3. 监控告警
- 定期检查分区负载均衡情况
- 监控消息积压情况
- 设置分区异常告警

## 故障排查

### 1. 分区分配异常
- 检查SymbolPartitioner是否正确配置
- 验证symbol是否为null或空字符串
- 查看Kafka集群分区状态

### 2. 消息乱序
- 确认相同symbol的消息是否发送到同一分区
- 检查消费者是否按分区顺序消费
- 验证消息时间戳是否正确

### 3. 性能问题
- 分析分区负载分布
- 检查是否存在热点分区
- 考虑调整分区数量或分区策略
