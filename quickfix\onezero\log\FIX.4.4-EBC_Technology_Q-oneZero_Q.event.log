20250722-07:46:59: Session FIX.4.4:EBC_Technology_Q->oneZero_Q schedule is daily, 00:00:00-UTC - 00:00:00-UTC
20250722-07:46:59: Created session: FIX.4.4:EBC_Technology_Q->oneZero_Q
20250722-07:47:00: Configured socket addresses for session: [taker5-conformance-env.onezero.com/***********:10183]
20250722-07:47:00: MINA session created: local=/**********:7954, class org.apache.mina.transport.socket.nio.NioSocketSession, remote=taker5-conformance-env.onezero.com/***********:10183
20250722-07:47:01: Initiated logon request
20250722-07:47:12: Disconnecting: Timed out waiting for logon response
20250722-07:47:12: Already disconnected: Socket exception (taker5-conformance-env.onezero.com/***********:10183): javax.net.ssl.SSLException: Improper close state: Status = OK HandshakeStatus = NEED_WRAP
bytesConsumed = 0 bytesProduced = 7 sequenceNumber = 1
