# 使用OpenJDK 17运行时镜像
FROM eclipse-temurin:17-jre-alpine

# 设置工作目录
WORKDIR /app

# 安装bash和创建目录
RUN apk add --no-cache bash && \
    mkdir -p /app/logs && \
    mkdir -p /app/quickfix

# 复制本地构建的jar包
COPY target/quote-oz-fix-*.jar app.jar
# 暴露端口
EXPOSE 8090

# 设置JVM参数，包含TLS v1.2配置和证书处理
#ENV JAVA_OPTS="-Xms512m -Xmx1024m -Djava.security.egd=file:/dev/./urandom -Dhttps.protocols=TLSv1.2 -Djdk.tls.client.protocols=TLSv1.2 -Dcom.sun.net.ssl.checkRevocation=false -Dsun.security.ssl.allowUnsafeRenegotiation=true -Dsun.security.ssl.allowLegacyHelloMessages=true -Djavax.net.ssl.trustStore=/app/certs/onezero-truststore.jks -Djavax.net.ssl.trustStorePassword=changeit -Djavax.net.ssl.trustStoreType=JKS"
ENV JAVA_OPTS="-Xms512m -Xmx1024m -Dfile.encoding=UTF-8 -Dconsole.encoding=UTF-8 "


# 启动应用
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
