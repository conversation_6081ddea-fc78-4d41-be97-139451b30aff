# Kafka配置文件使用指南

## 概述

现在所有Kafka配置都通过`application.properties`配置文件进行管理，不再硬编码在Java代码中。这样可以：
- 方便不同环境的配置管理
- 无需重新编译即可调整参数
- 更好的配置版本控制

## 配置文件结构

### Producer配置
```properties
# 基础配置
spring.kafka.bootstrap-servers=localhost:9092

# 可靠性配置
spring.kafka.producer.acks=all                                    # 等待所有副本确认
spring.kafka.producer.retries=2147483647                         # 最大重试次数
spring.kafka.producer.max-in-flight-requests-per-connection=1    # 保证消息顺序
spring.kafka.producer.enable-idempotence=true                    # 启用幂等性

# 性能配置
spring.kafka.producer.batch-size=65536                           # 64KB批次大小
spring.kafka.producer.linger-ms=10                               # 批处理等待时间
spring.kafka.producer.buffer-memory=67108864                     # 64MB缓冲区
spring.kafka.producer.compression-type=lz4                       # LZ4压缩

# 超时配置
spring.kafka.producer.request-timeout-ms=30000                   # 30秒请求超时
spring.kafka.producer.delivery-timeout-ms=120000                 # 2分钟交付超时
```

### Consumer配置
```properties
# 基础配置
spring.kafka.consumer.group-id=bg-oz-quote-group

# 偏移量配置
spring.kafka.consumer.auto-offset-reset=earliest                 # 从最早消息开始
spring.kafka.consumer.enable-auto-commit=false                   # 手动提交

# 性能配置
spring.kafka.consumer.fetch-min-bytes=50000                      # 最小50KB才返回
spring.kafka.consumer.fetch-max-wait=500                         # 最多等待500ms
spring.kafka.consumer.max-poll-records=1000                      # 每次最多1000条

# 会话配置
spring.kafka.consumer.session-timeout-ms=30000                   # 30秒会话超时
spring.kafka.consumer.heartbeat-interval-ms=10000                # 10秒心跳间隔
spring.kafka.consumer.max-poll-interval-ms=300000                # 5分钟poll间隔
```

## 配置参数详解

### Producer关键参数

| 参数 | 默认值 | 推荐值 | 说明 |
|------|--------|--------|------|
| `acks` | 1 | all | 确认级别，all最可靠 |
| `retries` | 0 | MAX_VALUE | 重试次数，无限重试 |
| `batch-size` | 16384 | 65536 | 批次大小，影响吞吐量 |
| `linger-ms` | 0 | 10 | 等待时间，平衡延迟和吞吐量 |
| `buffer-memory` | 33554432 | 67108864 | 缓冲区大小 |
| `compression-type` | none | lz4 | 压缩算法 |

### Consumer关键参数

| 参数 | 默认值 | 推荐值 | 说明 |
|------|--------|--------|------|
| `enable-auto-commit` | true | false | 手动提交更可靠 |
| `fetch-min-bytes` | 1 | 50000 | 批量拉取提高效率 |
| `max-poll-records` | 500 | 1000 | 每次拉取记录数 |
| `session-timeout-ms` | 10000 | 30000 | 会话超时时间 |

## 环境配置示例

### 开发环境 (application-dev.properties)
```properties
# 开发环境 - 注重调试和快速反馈
spring.kafka.bootstrap-servers=localhost:9092
spring.kafka.producer.batch-size=16384
spring.kafka.producer.linger-ms=0
spring.kafka.consumer.max-poll-records=100
```

### 测试环境 (application-test.properties)
```properties
# 测试环境 - 模拟生产环境
spring.kafka.bootstrap-servers=test-kafka:9092
spring.kafka.producer.batch-size=32768
spring.kafka.producer.linger-ms=5
spring.kafka.consumer.max-poll-records=500
```

### 生产环境 (application-prod.properties)
```properties
# 生产环境 - 高性能配置
spring.kafka.bootstrap-servers=prod-kafka-1:9092,prod-kafka-2:9092,prod-kafka-3:9092
spring.kafka.producer.batch-size=65536
spring.kafka.producer.linger-ms=10
spring.kafka.consumer.max-poll-records=1000
```

## 配置优化建议

### 1. 根据消息大小调整批次
```properties
# 小消息（<1KB）
spring.kafka.producer.batch-size=32768
spring.kafka.producer.linger-ms=20

# 大消息（>10KB）
spring.kafka.producer.batch-size=131072
spring.kafka.producer.linger-ms=5
```

### 2. 根据延迟要求调整
```properties
# 低延迟要求
spring.kafka.producer.linger-ms=0
spring.kafka.consumer.fetch-max-wait=100

# 高吞吐量要求
spring.kafka.producer.linger-ms=50
spring.kafka.consumer.fetch-max-wait=1000
```

### 3. 根据网络条件调整
```properties
# 高延迟网络
spring.kafka.producer.request-timeout-ms=60000
spring.kafka.producer.delivery-timeout-ms=300000

# 低延迟网络
spring.kafka.producer.request-timeout-ms=15000
spring.kafka.producer.delivery-timeout-ms=60000
```

## 监控配置

### JMX监控
```properties
# 启用JMX监控
spring.jmx.enabled=true
management.endpoints.jmx.exposure.include=*
```

### 日志配置
```properties
# Kafka客户端日志级别
logging.level.org.apache.kafka=INFO
logging.level.org.springframework.kafka=DEBUG
```

## 故障排查

### 常见配置问题

1. **消息丢失**
   ```properties
   # 确保这些配置
   spring.kafka.producer.acks=all
   spring.kafka.producer.enable-idempotence=true
   spring.kafka.consumer.enable-auto-commit=false
   ```

2. **性能问题**
   ```properties
   # 检查这些配置
   spring.kafka.producer.batch-size=65536
   spring.kafka.producer.linger-ms=10
   spring.kafka.consumer.max-poll-records=1000
   ```

3. **连接超时**
   ```properties
   # 调整超时配置
   spring.kafka.producer.request-timeout-ms=30000
   spring.kafka.consumer.session-timeout-ms=30000
   ```

## 配置验证

启动应用时，可以通过日志验证配置是否生效：
```
INFO  - Producer配置: acks=all, batch-size=65536, linger-ms=10
INFO  - Consumer配置: auto-commit=false, max-poll-records=1000
```

## 最佳实践

1. **使用配置文件管理**: 所有参数都通过配置文件管理
2. **环境隔离**: 不同环境使用不同的配置文件
3. **参数调优**: 根据实际业务场景调整参数
4. **监控告警**: 设置关键指标的监控
5. **文档维护**: 及时更新配置文档
