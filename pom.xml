<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.5.3</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<groupId>com.flopotech</groupId>
	<artifactId>bg-oz-quote</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>bg-oz-quote</name>
	<description>bg-oz-quote for Spring Boot</description>
	<properties>
		<java.version>17</java.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<maven.compiler.encoding>UTF-8</maven.compiler.encoding>
	</properties>
	<dependencies>
		<!-- Spring Boot 依赖 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
		</dependency>
		<!-- 日志依赖 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-logging</artifactId>
		</dependency>

		<!-- QuickFIX/J 依赖 -->
		<dependency>
			<groupId>org.quickfixj</groupId>
			<artifactId>quickfixj-core</artifactId>
			<version>2.3.1</version>
		</dependency>

		<dependency>
			<groupId>org.quickfixj</groupId>
			<artifactId>quickfixj-messages-all</artifactId>
			<version>2.3.1</version>
		</dependency>

		<!-- SSL/TLS 支持 -->
		<dependency>
			<groupId>org.quickfixj</groupId>
			<artifactId>quickfixj-messages-fix44</artifactId>
			<version>2.3.1</version>
		</dependency>

		<!-- JSR-250 注解支持 (PostConstruct, PreDestroy) -->
		<dependency>
			<groupId>jakarta.annotation</groupId>
			<artifactId>jakarta.annotation-api</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<!-- Kafka支持 -->
		<dependency>
			<groupId>org.springframework.kafka</groupId>
			<artifactId>spring-kafka</artifactId>
		</dependency>


		<!-- Apache Commons Lang3 -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>

		<!-- Protocol Buffers -->
		<dependency>
			<groupId>com.google.protobuf</groupId>
			<artifactId>protobuf-java</artifactId>
			<version>4.31.1</version>
		</dependency>

		<!-- Protocol Buffers工具类 -->
		<dependency>
			<groupId>com.google.protobuf</groupId>
			<artifactId>protobuf-java-util</artifactId>
			<version>4.31.1</version>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<jvmArguments>
						-Dfile.encoding=UTF-8
						-Dconsole.encoding=UTF-8
						-Dlogging.charset.console=UTF-8
					</jvmArguments>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>
