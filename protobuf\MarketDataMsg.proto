syntax = "proto3";

package com.flopotech.bg.quote.oz.msg;

// OZ深度数据条目
message DepthData {
    // 价格类型: 0-bid, 1-offer
    int32 tp = 1;

    // 价格
    double px = 2;

    // 可交易量
    double sz = 3;

    // 报价条件: A = Tradeable Quote(可交易报价), I = Indicative Quote(参考报价)
    string condition = 4;

    // 发起者
    string ori = 5;

    // 报价单唯一标识
    string uqId = 6;
}

// OZ报价消息
message MarketData {
    // 时间戳 - 接收到OZ那边的发送时间，在fix标准请求头中
    int64 timestamp = 1;

    // 合约
    string symbol = 2;

    // 当前行情OZ的最优bid价 - OZ返回的MDEntryType=0
    double bid = 3;

    // 当前行情OZ的最优offer价 - OZ返回的MDEntryType=1
    double offer = 4;

    // 中间价 - (bid+offer)/2
    double mid = 5;

    // OZ的深度数据集合
    repeated DepthData data = 6;
}