package com.flopotech.quote.oz.fix.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import quickfix.Application;
import quickfix.DoNotSend;
import quickfix.FieldNotFound;
import quickfix.IncorrectDataFormat;
import quickfix.IncorrectTagValue;
import quickfix.Message;
import quickfix.RejectLogon;
import quickfix.Session;
import quickfix.SessionID;
import quickfix.SessionNotFound;
import quickfix.UnsupportedMessageType;
import quickfix.field.*;
import quickfix.field.ResetSeqNumFlag;
import quickfix.fix44.*;

import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * OneZero Quote FIX应用处理器
 * 专门处理与OneZero报价服务器的FIX 4.4通信
 */
@Component
public class OneZeroQuoteApplication implements Application {

    private static final Logger logger = LoggerFactory.getLogger(OneZeroQuoteApplication.class);

    private SessionID currentSessionID;
    private boolean isLoggedOn = false;

    @Value("${quickfix.onezero.password}")
    private String onzeroPassword;

    @Value("${quickfix.onezero.subscribe.symbol}")
    private String subSymbol;

    /**
     * 会话创建时调用
     */
    @Override
    public void onCreate(SessionID sessionID) {
        try {
            logger.info("OneZero Quote会话创建: {}", sessionID);
            this.currentSessionID = sessionID;

            // 获取会话详细信息
            try {
                quickfix.Session session = quickfix.Session.lookupSession(sessionID);
                if (session != null) {
                    logger.info("会话详情 - 发送方: {}, 接收方: {}, 版本: {}",
                            sessionID.getSenderCompID(),
                            sessionID.getTargetCompID(),
                            sessionID.getBeginString());
                }
            } catch (Exception e) {
                logger.warn("获取会话详情失败", e);
            }
        } catch (Exception e) {
            logger.error("会话创建过程中发生错误", e);
        }
    }


    /**
     * 会话登录时调用
     */
    @Override
    public void onLogon(SessionID sessionID) {
        logger.info("OneZero Quote会话登录成功: {}", sessionID);
        this.isLoggedOn = true;

        // 登录成功后，可以发送市场数据订阅请求
        try {
            subscribeToMarketData(sessionID);
        } catch (Exception e) {
            logger.error("订阅市场数据失败", e);
        }
    }

    /**
     * 会话登出时调用
     */
    @Override
    public void onLogout(SessionID sessionID) {
        logger.error("OneZero Quote会话登出: {}", sessionID);
        this.isLoggedOn = false;

        // 详细分析登出原因
        try {
            logger.error("=== 会话登出分析 ===");

            quickfix.Session session = quickfix.Session.lookupSession(sessionID);
            if (session != null) {
                logger.error("会话状态: 连接={}, 登录={}, 启用={}",
                        session.isSessionTime(),
                        session.isLoggedOn(),
                        session.isEnabled());

                // 获取最后的错误信息
                try {
                    logger.error("会话详情: 发送序号={}, 接收序号={}",
                            session.getExpectedSenderNum(),
                            session.getExpectedTargetNum());
                } catch (Exception e) {
                    logger.error("无法获取序号信息", e);
                }
            }
            logger.error("=== 会话登出分析结束 ===");

        } catch (Exception e) {
            logger.error("获取会话状态失败", e);
        }
    }

    /**
     * 管理消息发送
     */
    @Override
    public void toAdmin(quickfix.Message message, SessionID sessionID) {
        logger.info("发送管理消息到OneZero: {} to {}", message.getClass().getSimpleName(), sessionID);

        try {
            // 如果是登录消息，添加密码（不需要Username字段）
            if (message instanceof Logon) {
                // 只添加密码，不添加用户名
                message.setField(new Password(onzeroPassword));
                // 添加序号重置标志
                message.setField(new ResetSeqNumFlag(true));
                // 显示可读格式的完整登录消息
                String readableLoginMessage = message.toString().replace('\u0001', '|');
                logger.info("可读格式登录消息: {}", readableLoginMessage);
                logger.info("=== 登录消息详情结束 ===");
            }
            logger.info("消息类型: {}", message.getClass().getName());
            // 打印完整的FIX消息内容
            printFixMessageDetails(message, "发送", sessionID);

        } catch (Exception e) {
            logger.error("处理管理消息时发生错误", e);
        }
    }
    /**
     * 管理消息接收
     */
    @Override
    public void fromAdmin(Message message, SessionID sessionID)
            throws FieldNotFound, IncorrectDataFormat, IncorrectTagValue, RejectLogon {
        logger.info("接收OneZero管理消息: {} from {}", message.getClass().getSimpleName(), sessionID);
        logger.info(message.getClass().getName());
        // 打印完整的FIX消息内容
        printFixMessageDetails(message, "接收", sessionID);

        if (message instanceof Logon) {
            logger.info("✅ 收到OneZero登录确认");
        } else if (message instanceof Logout) {
            logger.error("❌ 收到OneZero登出消息");
            analyzeLogoutReason(message, sessionID);
        } else if (message instanceof BusinessMessageReject) {
            logger.error("❌ 收到业务消息拒绝");
            analyzeBusinessReject((BusinessMessageReject) message, sessionID);
        } else if (message instanceof Reject) {
            logger.error("❌ 收到OneZero拒绝消息");
            try {
                // 尝试获取拒绝原因
                if (message.isSetField(58)) { // Text字段
                    String rejectText = message.getString(58);
                    logger.error("拒绝原因: {}", rejectText);
                }
                if (message.isSetField(373)) { // SessionRejectReason
                    int rejectReason = message.getInt(373);
                    logger.error("拒绝代码: {}", rejectReason);
                }
            } catch (Exception e) {
                logger.error("解析拒绝消息失败", e);
            }
        }
    }
    /**
     * 应用消息发送
     */
    @Override
    public void toApp(Message message, SessionID sessionID) throws DoNotSend {
        logger.info("发送应用消息到OneZero: {} to {}", message.getClass().getSimpleName(), sessionID);

        // 打印完整的FIX消息内容
        printFixMessageDetails(message, "发送应用", sessionID);
    }

    /**
     * 应用消息接收 - 处理OneZero的报价数据
     */
    @Override
    public void fromApp(Message message, SessionID sessionID)
            throws FieldNotFound, IncorrectDataFormat, IncorrectTagValue, UnsupportedMessageType {
        logger.info("接收OneZero应用消息: {} from {}", message.getClass().getSimpleName(), sessionID);

        // 打印完整的FIX消息内容
        printFixMessageDetails(message, "接收应用", sessionID);

        try {
            // 处理市场数据快照
            if (message instanceof MarketDataSnapshotFullRefresh) {
                handleMarketDataSnapshot((MarketDataSnapshotFullRefresh) message);
            }
            // 处理市场数据增量刷新
            else if (message instanceof MarketDataIncrementalRefresh) {
                handleMarketDataIncremental((MarketDataIncrementalRefresh) message);
            }
            // 处理市场数据请求拒绝
            else if (message instanceof MarketDataRequestReject) {
                handleMarketDataRequestReject((MarketDataRequestReject) message);
            }
            // 处理安全定义
            else if (message instanceof SecurityDefinition) {
                handleSecurityDefinition((SecurityDefinition) message);
            }
            // 处理测试请求
            else if (message instanceof TestRequest) {
                handleTestRequest((TestRequest) message, sessionID);
            } else {
                logger.warn("未处理的OneZero消息类型: {}", message.getClass().getSimpleName());
            }
        } catch (Exception e) {
            logger.error("处理OneZero FIX消息时发生错误", e);
        }
    }

    /**
     * 订阅市场数据
     */
    private void subscribeToMarketData(SessionID sessionID) throws SessionNotFound {
        MarketDataRequest request = new MarketDataRequest();

        // 设置请求ID
        request.set(new MDReqID("MD_REQ_" + System.currentTimeMillis()));

        // 设置订阅类型：0=快照, 1=快照+更新, 2=禁用之前的快照+更新请求
        request.set(new SubscriptionRequestType('1')); // '1' = SNAPSHOT_PLUS_UPDATES

        // 设置市场深度：0=完整深度, 1=顶级深度
        request.set(new MarketDepth(5));

        // 设置更新类型：0=完整刷新, 1=增量刷新
        request.set(new MDUpdateType(MDUpdateType.INCREMENTAL_REFRESH));

        // 添加要订阅的数据类型
        MarketDataRequest.NoMDEntryTypes entryTypesGroup = new MarketDataRequest.NoMDEntryTypes();
        entryTypesGroup.set(new MDEntryType(MDEntryType.BID)); // 买价
        request.addGroup(entryTypesGroup);

        entryTypesGroup = new MarketDataRequest.NoMDEntryTypes();
        entryTypesGroup.set(new MDEntryType(MDEntryType.OFFER)); // 卖价
        request.addGroup(entryTypesGroup);
        String [] symbols=subSymbol.split(",");
        // 添加要订阅的货币对
        for(String s:symbols){
            MarketDataRequest.NoRelatedSym symbolGroup = new MarketDataRequest.NoRelatedSym();
            symbolGroup.set(new Symbol(s));
            request.addGroup(symbolGroup);
        }

        // 发送市场数据请求
        Session.sendToTarget(request, sessionID);
        logger.info("已发送市场数据订阅请求到OneZero");
    }

    /**
     * 处理市场数据快照
     */
    private void handleMarketDataSnapshot(MarketDataSnapshotFullRefresh message) throws FieldNotFound {
        String symbol = message.getString(Symbol.FIELD);
        logger.info("收到OneZero市场数据快照: {}", symbol);
        LocalDateTime d=message.getUtcTimeStamp(SendingTime.FIELD);
        Long s=d.atZone(ZoneId.of("UTC")).toInstant().toEpochMilli();
        // 处理市场数据条目
        int noMDEntries = message.getInt(NoMDEntries.FIELD);
        for (int i = 1; i <= noMDEntries; i++) {
            MarketDataSnapshotFullRefresh.NoMDEntries group = new MarketDataSnapshotFullRefresh.NoMDEntries();
            message.getGroup(i, group);

            char entryType = group.getChar(MDEntryType.FIELD);
            double price = group.getDouble(MDEntryPx.FIELD);
            double size = group.getDouble(MDEntrySize.FIELD);

            String entryTypeStr = (entryType == MDEntryType.BID) ? "BID" : "OFFER";
            logger.info("  {} - {}: Price={}, Size={}", symbol, entryTypeStr, price, size);
        }
    }

    /**
     * 处理市场数据增量刷新
     */
    private void handleMarketDataIncremental(MarketDataIncrementalRefresh message) throws FieldNotFound {
        logger.info("收到OneZero市场数据增量刷新");

        int noMDEntries = message.getInt(NoMDEntries.FIELD);
        for (int i = 1; i <= noMDEntries; i++) {
            MarketDataIncrementalRefresh.NoMDEntries group = new MarketDataIncrementalRefresh.NoMDEntries();
            message.getGroup(i, group);

            String symbol = group.getString(Symbol.FIELD);
            char entryType = group.getChar(MDEntryType.FIELD);
            double price = group.getDouble(MDEntryPx.FIELD);

            String entryTypeStr = (entryType == MDEntryType.BID) ? "BID" : "OFFER";
            logger.info("  {} - {}: Price={}", symbol, entryTypeStr, price);
        }
    }

    /**
     * 处理市场数据请求拒绝
     */
    private void handleMarketDataRequestReject(MarketDataRequestReject message) throws FieldNotFound {
        String mdReqID = message.getString(MDReqID.FIELD);
        String rejectReason = message.isSetField(Text.FIELD) ? message.getString(Text.FIELD) : "未知原因";
        logger.warn("OneZero市场数据请求被拒绝 - ID: {}, 原因: {}", mdReqID, rejectReason);
    }

    /**
     * 处理安全定义
     */
    private void handleSecurityDefinition(SecurityDefinition message) throws FieldNotFound {
        String symbol = message.getString(Symbol.FIELD);
        logger.info("收到OneZero安全定义: {}", symbol);
    }

    /**
     * 处理测试请求
     */
    private void handleTestRequest(TestRequest message, SessionID sessionID) throws FieldNotFound, SessionNotFound {
        String testReqID = message.getString(TestReqID.FIELD);

        // 发送心跳响应
        Heartbeat heartbeat = new Heartbeat();
        heartbeat.set(new TestReqID(testReqID));

        Session.sendToTarget(heartbeat, sessionID);
        logger.debug("响应OneZero测试请求: {}", testReqID);
    }

    /**
     * 检查是否已登录
     */
    public boolean isLoggedOn() {
        return isLoggedOn;
    }

    /**
     * 获取当前会话ID
     */
    public SessionID getCurrentSessionID() {
        return currentSessionID;
    }

    /**
     * 打印FIX消息详细信息
     */
    private void printFixMessageDetails(Message message, String direction, SessionID sessionID) {
        try {
            if (message == null) {
                logger.warn("消息为空，无法打印详情");
                return;
            }

            String messageString = message.toString();
            // 将SOH分隔符替换为可读的|符号
            String readableMessage = messageString.replace('\u0001', '|');

            logger.info("=== {} FIX消息详情 ===", direction);
            logger.info("会话: {}", sessionID != null ? sessionID.toString() : "null");
            logger.info("消息类型: {}", message.getClass().getSimpleName());
            logger.info("完整消息: {}", readableMessage);
            logger.info("=== {} FIX消息详情结束 ===", direction);

        } catch (Exception e) {
            logger.error("解析FIX消息失败", e);
        }
    }

    /**
     * 分析登出原因
     */
    private void analyzeLogoutReason(Message message, SessionID sessionID) {
        try {
            if (message == null) {
                logger.error("登出消息为空");
                return;
            }

            logger.error("=== 登出消息分析 ===");

            // 尝试获取登出原因
            if (message.isSetField(58)) { // Text字段
                String logoutText = message.getString(58);
                logger.error("登出原因文本: {}", logoutText);
            }

            // 检查其他可能的字段
            if (message.isSetField(373)) { // SessionRejectReason
                int rejectReason = message.getInt(373);
                logger.error("会话拒绝代码: {}", rejectReason);
            }

            // 打印完整消息
            logger.error("完整登出消息: {}", message.toString());

            // 获取会话状态
            quickfix.Session session = quickfix.Session.lookupSession(sessionID);
            if (session != null) {
                logger.error("会话状态 - 连接: {}, 登录: {}, 启用: {}",
                        session.isSessionTime(),
                        session.isLoggedOn(),
                        session.isEnabled());
            }

            logger.error("=== 登出消息分析结束 ===");

        } catch (Exception e) {
            logger.error("解析登出消息失败", e);
        }
    }

    /**
     * 分析业务消息拒绝原因
     */
    private void analyzeBusinessReject(BusinessMessageReject message, SessionID sessionID) {
        try {
            if (message == null) {
                logger.error("业务拒绝消息为空");
                return;
            }

            logger.error("=== 业务消息拒绝分析 ===");

            if (message.isSetField(58)) { // Text
                String rejectText = message.getString(58);
                logger.error("拒绝原因: {}", rejectText);
            }

            if (message.isSetField(380)) { // BusinessRejectReason
                int rejectReason = message.getInt(380);
                logger.error("业务拒绝代码: {}", rejectReason);
            }

            if (message.isSetField(372)) { // RefMsgType
                String refMsgType = message.getString(372);
                logger.error("被拒绝的消息类型: {}", refMsgType);
            }

            logger.error("完整拒绝消息: {}", message.toString());
            logger.error("=== 业务消息拒绝分析结束 ===");

        } catch (Exception e) {
            logger.error("解析业务拒绝消息失败", e);
        }
    }

}
