# Kafka高吞吐量配置说明

## 概述

本项目针对市场数据处理场景，配置了高吞吐量的Kafka生产者和消费者，同时确保消息的可靠性和顺序性。

## 核心配置策略

### 1. 生产者配置 (Producer)

#### 可靠性配置
```properties
# 必须等待所有副本确认
spring.kafka.producer.acks=all

# 无限重试确保消息不丢失
spring.kafka.producer.retries=2147483647

# 保证消息顺序
spring.kafka.producer.max-in-flight-requests-per-connection=1

# 启用幂等性，避免重复消息
spring.kafka.producer.enable-idempotence=true
```

#### 高吞吐量配置
```properties
# 64KB批次大小，提高批处理效率
spring.kafka.producer.batch-size=65536

# 等待10ms收集更多消息进行批处理
spring.kafka.producer.linger-ms=10

# 64MB缓冲区，支持更多消息缓存
spring.kafka.producer.buffer-memory=67108864

# LZ4压缩，平衡压缩率和CPU消耗
spring.kafka.producer.compression-type=lz4
```

#### 网络优化配置
```properties
# 30秒请求超时
spring.kafka.producer.request-timeout-ms=30000

# 2分钟交付超时
spring.kafka.producer.delivery-timeout-ms=120000
```

### 2. 消费者配置 (Consumer)

#### 高吞吐量配置
```properties
# 手动提交，确保消息处理完成后才提交
spring.kafka.consumer.enable-auto-commit=false

# 最小50KB才返回，减少网络往返
spring.kafka.consumer.fetch-min-bytes=50000

# 最多等待500ms，平衡延迟和吞吐量
spring.kafka.consumer.fetch-max-wait=500

# 每次poll最多1000条记录
spring.kafka.consumer.max-poll-records=1000
```

#### 网络缓冲区配置
```properties
# 256KB接收缓冲区
spring.kafka.consumer.receive-buffer-config=262144

# 128KB发送缓冲区
spring.kafka.consumer.send-buffer-config=131072
```

#### 会话管理配置
```properties
# 30秒会话超时
spring.kafka.consumer.session-timeout-ms=30000

# 10秒心跳间隔
spring.kafka.consumer.heartbeat-interval-ms=10000

# 5分钟poll间隔
spring.kafka.consumer.max-poll-interval-ms=300000
```

### 3. 容器配置

#### 并发处理
```java
// 并发数等于分区数，最大化并行处理
factory.setConcurrency(partitions);

// 手动立即确认模式
factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL_IMMEDIATE);
```

#### 错误处理
```java
// 固定退避策略：1秒间隔，重试3次
factory.setCommonErrorHandler(new DefaultErrorHandler(
    new FixedBackOff(1000L, 3L)));
```

## 性能优化要点

### 1. 同步发送策略
- **优势**: 确保消息必须发送成功，提供强一致性保证
- **实现**: 使用`kafkaTemplate.send().get()`进行同步发送
- **适用场景**: 金融市场数据等对可靠性要求极高的场景

### 2. 批处理优化
- **批次大小**: 64KB，平衡内存使用和网络效率
- **等待时间**: 10ms，在延迟和吞吐量之间取得平衡
- **压缩算法**: LZ4，提供良好的压缩率和低CPU消耗

### 3. 分区策略
- **自定义分区器**: 按symbol进行分区
- **顺序保证**: 相同symbol的消息发送到同一分区
- **负载均衡**: 不同symbol分布到不同分区

### 4. 消费者优化
- **手动确认**: 确保消息处理完成后才提交offset
- **批量拉取**: 每次最多拉取1000条消息
- **并发处理**: 消费者数量等于分区数量

## 性能指标

### 预期性能
- **吞吐量**: 10万+ 消息/秒 (取决于消息大小和硬件配置)
- **延迟**: P99 < 50ms (在正常网络条件下)
- **可靠性**: 99.99% 消息不丢失

### 监控指标
- **生产者指标**: 发送速率、批次大小、压缩率
- **消费者指标**: 消费速率、处理延迟、积压消息数
- **分区指标**: 分区负载分布、消息顺序性

## 调优建议

### 1. 根据业务场景调整
```properties
# 对于超低延迟场景
spring.kafka.producer.linger-ms=0
spring.kafka.producer.batch-size=16384

# 对于超高吞吐量场景
spring.kafka.producer.linger-ms=50
spring.kafka.producer.batch-size=131072
```

### 2. 硬件优化
- **网络**: 使用万兆网卡，减少网络延迟
- **存储**: 使用SSD存储，提高I/O性能
- **内存**: 增加JVM堆内存，支持更大的缓冲区

### 3. JVM调优
```bash
# 推荐JVM参数
-Xms4g -Xmx4g
-XX:+UseG1GC
-XX:MaxGCPauseMillis=20
-XX:+UnlockExperimentalVMOptions
-XX:+UseJVMCICompiler
```

## 故障处理

### 1. 消息积压
- **原因**: 消费速度跟不上生产速度
- **解决**: 增加消费者数量，优化处理逻辑

### 2. 网络超时
- **原因**: 网络延迟过高或Kafka集群负载过重
- **解决**: 调整超时参数，优化网络配置

### 3. 内存不足
- **原因**: 缓冲区配置过大或消息积压
- **解决**: 调整缓冲区大小，增加JVM内存

## 最佳实践

1. **监控告警**: 设置关键指标的监控和告警
2. **容量规划**: 根据业务增长预估容量需求
3. **定期测试**: 进行压力测试验证性能指标
4. **版本升级**: 及时升级Kafka版本获得性能改进
5. **配置备份**: 备份重要的配置参数
