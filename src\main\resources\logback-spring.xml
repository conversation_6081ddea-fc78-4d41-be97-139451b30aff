<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 应用名称 -->
    <property name="APP_NAME" value="quote-oz-fix"/>
    <!-- 日志文件保留天数 -->
    <property name="LOG_MAX_HISTORY" value="7"/>
    <!-- 日志文件保存路径 -->
    <property name="LOG_APP_HOME" value="logs"/>

    <!-- 彩色日志 -->
    <!-- 彩色日志依赖的渲染类 -->
    <conversionRule conversionWord="clr" class="org.springframework.boot.logging.logback.ColorConverter" />
    <conversionRule conversionWord="wex" class="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter" />
    <conversionRule conversionWord="wEx" class="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter" />
    <!-- 彩色日志格式 -->
    <property name="CONSOLE_LOG_PATTERN" value="${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%10.10t]){faint} %clr(%-20.20logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}" />

    <!-- 控制台输出 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 应用日志文件 -->
    <appender name="APP" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_APP_HOME}/${APP_NAME}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_APP_HOME}/${APP_NAME}.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>${LOG_MAX_HISTORY}</maxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- QuickFIX日志 -->
    <appender name="QUICKFIX" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_APP_HOME}/quickfix/quickfix.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_APP_HOME}/quickfix/quickfix.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    <!-- 日志级别配置 -->
    <logger name="com.fudianyun" level="DEBUG"/>
    <logger name="quickfix" level="INFO" additivity="false">
        <appender-ref ref="QUICKFIX"/>
        <appender-ref ref="STDOUT"/>
    </logger>
    <logger name="org.springframework" level="INFO"/>
    <logger name="org.apache" level="WARN"/>
    <logger name="org.apache.mina" level="WARN"/>
    <logger name="org.hibernate" level="INFO"/>
    <logger name="druid.sql" level="INFO"/>
    <logger name="httpclient" level="INFO"/>

    <!-- 根日志级别 -->
    <root level="INFO">
        <appender-ref ref="APP"/>
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>
