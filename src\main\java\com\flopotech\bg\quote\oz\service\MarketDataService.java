package com.flopotech.bg.quote.oz.service;

import com.flopotech.bg.quote.oz.msg.MarketDataMsg;
import com.flopotech.bg.quote.oz.util.MarketDataBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 市场数据服务
 * 整合市场数据的处理和发送功能
 */
@Service
public class MarketDataService {

    private static final Logger logger = LoggerFactory.getLogger(MarketDataService.class);

    @Autowired
    private MarketDataProducer marketDataProducer;

    @Autowired
    private MarketDataBuilder marketDataBuilder;

    /**
     * 处理并发送市场数据
     *
     * @param symbol    合约
     * @param bid       最优bid价
     * @param offer     最优offer价
     * @param timestamp 时间戳
     */
    public void processAndSendMarketData(String symbol, double bid, double offer, long timestamp) {
        try {
            // 创建市场数据消息
            MarketDataMsg.MarketData marketData = marketDataBuilder.createMarketData(
                    timestamp, symbol, bid, offer);

            // 验证数据有效性
            if (marketDataBuilder.isValidMarketData(marketData)) {
                // 同步发送到Kafka
                SendResult<String, byte[]> result = marketDataProducer.sendMarketData(marketData);

                if (result != null) {
                    logger.info("成功处理并发送市场数据 - Symbol: {}, Bid: {}, Offer: {}, Mid: {}, Partition: {}, Offset: {}",
                            symbol, bid, offer, marketData.getMid(),
                            result.getRecordMetadata().partition(), result.getRecordMetadata().offset());
                } else {
                    logger.error("发送市场数据失败 - Symbol: {}, Bid: {}, Offer: {}", symbol, bid, offer);
                }
            } else {
                logger.warn("市场数据验证失败 - Symbol: {}, Bid: {}, Offer: {}", symbol, bid, offer);
            }

        } catch (Exception e) {
            logger.error("处理市场数据异常 - Symbol: {}, Error: {}", symbol, e.getMessage(), e);
        }
    }

    /**
     * 处理并发送包含深度数据的市场数据
     *
     * @param symbol      合约
     * @param bid         最优bid价
     * @param offer       最优offer价
     * @param timestamp   时间戳
     * @param bidDepths   买方深度数据
     * @param offerDepths 卖方深度数据
     */
    public void processAndSendMarketDataWithDepth(String symbol, double bid, double offer, long timestamp,
            List<DepthInfo> bidDepths, List<DepthInfo> offerDepths) {
        try {
            // 构建深度数据列表
            List<MarketDataMsg.DepthData> depthDataList = new ArrayList<>();

            // 添加买方深度数据
            if (bidDepths != null) {
                for (DepthInfo depthInfo : bidDepths) {
                    MarketDataMsg.DepthData depthData = marketDataBuilder.createDepthData(
                            0, // bid类型
                            depthInfo.getPrice(),
                            depthInfo.getSize(),
                            depthInfo.getCondition(),
                            depthInfo.getOrigin(),
                            depthInfo.getUniqueId());
                    depthDataList.add(depthData);
                }
            }

            // 添加卖方深度数据
            if (offerDepths != null) {
                for (DepthInfo depthInfo : offerDepths) {
                    MarketDataMsg.DepthData depthData = marketDataBuilder.createDepthData(
                            1, // offer类型
                            depthInfo.getPrice(),
                            depthInfo.getSize(),
                            depthInfo.getCondition(),
                            depthInfo.getOrigin(),
                            depthInfo.getUniqueId());
                    depthDataList.add(depthData);
                }
            }

            // 创建包含深度数据的市场数据消息
            MarketDataMsg.MarketData marketData = marketDataBuilder.createMarketDataWithDepth(
                    timestamp, symbol, bid, offer, depthDataList);

            // 验证数据有效性
            if (marketDataBuilder.isValidMarketData(marketData)) {
                // 同步发送到Kafka
                SendResult<String, byte[]> result = marketDataProducer.sendMarketData(marketData);

                if (result != null) {
                    logger.info(
                            "成功处理并发送深度市场数据 - Symbol: {}, Bid: {}, Offer: {}, DepthCount: {}, Partition: {}, Offset: {}",
                            symbol, bid, offer, depthDataList.size(),
                            result.getRecordMetadata().partition(), result.getRecordMetadata().offset());
                } else {
                    logger.error("发送深度市场数据失败 - Symbol: {}, Bid: {}, Offer: {}", symbol, bid, offer);
                }
            } else {
                logger.warn("深度市场数据验证失败 - Symbol: {}, Bid: {}, Offer: {}", symbol, bid, offer);
            }

        } catch (Exception e) {
            logger.error("处理深度市场数据异常 - Symbol: {}, Error: {}", symbol, e.getMessage(), e);
        }
    }

    /**
     * 深度信息内部类
     */
    public static class DepthInfo {
        private double price;
        private double size;
        private String condition;
        private String origin;
        private String uniqueId;

        public DepthInfo(double price, double size, String condition, String origin, String uniqueId) {
            this.price = price;
            this.size = size;
            this.condition = condition;
            this.origin = origin;
            this.uniqueId = uniqueId;
        }

        // Getters
        public double getPrice() {
            return price;
        }

        public double getSize() {
            return size;
        }

        public String getCondition() {
            return condition;
        }

        public String getOrigin() {
            return origin;
        }

        public String getUniqueId() {
            return uniqueId;
        }

        // Setters
        public void setPrice(double price) {
            this.price = price;
        }

        public void setSize(double size) {
            this.size = size;
        }

        public void setCondition(String condition) {
            this.condition = condition;
        }

        public void setOrigin(String origin) {
            this.origin = origin;
        }

        public void setUniqueId(String uniqueId) {
            this.uniqueId = uniqueId;
        }
    }
}
