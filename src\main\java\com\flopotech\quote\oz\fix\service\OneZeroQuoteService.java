package com.flopotech.quote.oz.fix.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import quickfix.*;

import java.util.HashMap;
import java.util.Map;

/**
 * OneZero Quote服务管理类
 * 负责管理与OneZero报价服务器的FIX连接
 */
@Service
public class OneZeroQuoteService {

    private static final Logger logger = LoggerFactory.getLogger(OneZeroQuoteService.class);

    @Autowired
    private SocketInitiator initiator;

    @Autowired
    private OneZeroQuoteApplication oneZeroQuoteApplication;

    /**
     * 获取连接状态
     */
    public Map<String, Object> getStatus() {
        Map<String, Object> status = new HashMap<>();

        try {
            boolean isStarted = initiator.isLoggedOn();
            status.put("connected", isStarted);
            status.put("status", isStarted ? "CONNECTED" : "DISCONNECTED");

            // 获取会话信息
            if (isStarted) {
                status.put("sessions", initiator.getSessions().size());
                status.put("sessionIds", initiator.getSessions().toString());
            } else {
                status.put("sessions", 0);
                status.put("sessionIds", "[]");
            }

            status.put("timestamp", System.currentTimeMillis());

        } catch (Exception e) {
            logger.error("获取OneZero状态失败", e);
            status.put("connected", false);
            status.put("status", "ERROR");
            status.put("error", e.getMessage());
        }

        return status;
    }

    /**
     * 启动连接
     */
    public void startConnection() throws ConfigError {
        try {
            if (initiator.isLoggedOn()) {
                logger.warn("OneZero连接已经存在");
                return;
            }

            initiator.start();
            logger.info("OneZero Quote连接已启动");

        } catch (Exception e) {
            logger.error("启动OneZero连接失败", e);
            throw e;
        }
    }

    /**
     * 停止连接
     */
    public void stopConnection() {
        try {
            if (initiator != null) {
                initiator.stop();
                logger.info("OneZero Quote连接已停止");
            }
        } catch (Exception e) {
            logger.error("停止OneZero连接时发生错误", e);
        }
    }

    /**
     * 重启连接
     */
    public void restartConnection() {
        logger.info("重启OneZero连接...");
        stopConnection();
        try {
            Thread.sleep(2000); // 等待2秒
            startConnection();
        } catch (Exception e) {
            logger.error("重启OneZero连接失败", e);
        }
    }

    /**
     * 检查是否已连接
     */
    public boolean isConnected() {
        try {
            return initiator != null && initiator.isLoggedOn();
        } catch (Exception e) {
            logger.error("检查连接状态失败", e);
            return false;
        }
    }

    /**
     * 获取会话数量
     */
    public int getSessionCount() {
        try {
            return initiator != null ? initiator.getSessions().size() : 0;
        } catch (Exception e) {
            logger.error("获取会话数量失败", e);
            return 0;
        }
    }

    /**
     * 检查是否已登录
     */
    public boolean isLoggedOn() {
        try {
            if (oneZeroQuoteApplication != null) {
                return oneZeroQuoteApplication.isLoggedOn();
            }
            return false;
        } catch (Exception e) {
            logger.error("检查登录状态失败", e);
            return false;
        }
    }

    /**
     * 获取当前会话ID
     */
    public quickfix.SessionID getCurrentSessionID() {
        try {
            if (oneZeroQuoteApplication != null) {
                return oneZeroQuoteApplication.getCurrentSessionID();
            }
            return null;
        } catch (Exception e) {
            logger.error("获取当前会话ID失败", e);
            return null;
        }
    }
}