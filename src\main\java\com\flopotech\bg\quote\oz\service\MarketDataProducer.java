package com.flopotech.bg.quote.oz.service;

import com.flopotech.bg.quote.oz.msg.MarketDataMsg;
import com.flopotech.bg.quote.oz.util.PartitionMonitor;
import com.flopotech.bg.quote.oz.util.ProtobufSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;

/**
 * 市场数据Kafka生产者服务
 * 负责将市场数据发送到Kafka Topic
 */
@Service
public class MarketDataProducer {

    private static final Logger logger = LoggerFactory.getLogger(MarketDataProducer.class);

    @Autowired
    private KafkaTemplate<String, byte[]> kafkaTemplate;

    @Autowired
    private ProtobufSerializer protobufSerializer;

    @Autowired
    private PartitionMonitor partitionMonitor;

    @Value("${kafka.topic.market-data}")
    private String marketDataTopic;

    /**
     * 同步发送市场数据到Kafka - 高吞吐量配置
     *
     * @param marketData 市场数据消息
     * @return 发送结果
     */
    public SendResult<String, byte[]> sendMarketData(MarketDataMsg.MarketData marketData) {
        if (marketData == null) {
            logger.warn("尝试发送null市场数据");
            return null;
        }

        try {
            // 序列化protobuf消息
            byte[] serializedData = protobufSerializer.serialize(marketData);

            if (serializedData == null) {
                logger.error("序列化市场数据失败，symbol: {}", marketData.getSymbol());
                return null;
            }

            // 使用symbol作为key，确保同一合约的数据发送到同一分区
            String key = marketData.getSymbol();

            // 同步发送消息 - 确保消息必须发送成功
            SendResult<String, byte[]> result = kafkaTemplate.send(marketDataTopic, key, serializedData).get();

            // 记录分区信息
            partitionMonitor.createMonitorCallback(key).onCompletion(result.getRecordMetadata(), null);

            logger.debug("成功发送市场数据到Kafka - Symbol: {}, Topic: {}, Partition: {}, Offset: {}",
                    marketData.getSymbol(),
                    result.getRecordMetadata().topic(),
                    result.getRecordMetadata().partition(),
                    result.getRecordMetadata().offset());

            return result;

        } catch (Exception e) {
            logger.error("发送市场数据异常 - Symbol: {}, Error: {}",
                    marketData.getSymbol(), e.getMessage(), e);

            // 记录失败信息
            partitionMonitor.createMonitorCallback(marketData.getSymbol()).onCompletion(null, e);

            // 重新抛出异常，让调用方知道发送失败
            throw new RuntimeException("发送市场数据到Kafka失败: " + e.getMessage(), e);
        }
    }

}
